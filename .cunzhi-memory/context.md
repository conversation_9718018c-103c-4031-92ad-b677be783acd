# 项目上下文信息

- 用户要求在向量化模块和检索模块中加入知识图谱功能，需要实现图谱-向量混合检索策略，包括实体向量化、关系推理、图谱增强检索等核心功能
- 已成功新增4-7-Text2SQL检索模块.md作为第7个检索子模块，专门处理结构化数据查询需求，包含SpringAI集成方案和多数据源支持，同时更新了4-检索模块.md、README.md、0-RAG技术方案总览.md等相关文档，形成稠密向量+稀疏向量+知识图谱+结构化数据的四重混合检索架构
- 已完成Text2SQL模块的全面优化：1.完善了用户提问到自然语言回答的完整流程图；2.新增半结构化数据处理方案（JSON/XML/CSV处理和查询转换）；3.提供NoSQL数据库选型方案（MongoDB/Elasticsearch/Redis）和查询适配器；4.实现完整的SpringAI Text2SQL案例，包含表结构向量化、智能SQL生成、自然语言回答等核心功能
- 已完成飞书文档同步方案的完整集成：1.在文档提取模块新增飞书文档处理器，支持飞书文档、表格、多维表格、思维导图、白板等类型；2.实现飞书API客户端和Webhook事件监听服务；3.新增定时同步调度器，支持增量和全量同步；4.集成协作信息提取和语义增强功能；5.更新流程图和配置文件；6.更新README.md和总览文档，完整集成飞书生态到RAG系统架构中
- 新增独立的飞书文档同步方案文档1-1-飞书文档同步方案.md，包含完整的技术方案说明、流程图、核心功能架构、配置部署、监控运维和扩展优化等内容，并更新README.md文档结构
- 新增通用文档同步协议文档1-2-通用文档同步协议.md，设计了标准化的多数据源文档同步框架，支持飞书、语雀、Notion等多平台接入，包含统一接口定义、事件模型、配置管理、同步策略、监控运维等完整方案，实现"一次开发，多平台复用"的设计目标
