# 通用文档同步协议 (Universal Document Sync Protocol)

## 方案说明

### 1. 技术方案概述
通用文档同步协议是一个标准化的多数据源文档同步框架，旨在为RAG系统提供统一的文档接入能力。该协议定义了标准化的接口、数据模型和同步机制，支持飞书、语雀、Notion、钉钉、企业微信等多种在线文档平台的无缝接入，实现了"一次开发，多平台复用"的设计理念。

### 2. 核心设计理念
- **协议标准化**：定义统一的文档同步协议和数据格式
- **插件化架构**：每个数据源作为独立插件，支持热插拔
- **配置驱动**：通过配置文件驱动不同数据源的接入
- **事件驱动**：统一的事件模型处理各种文档变更
- **可扩展性**：易于扩展新的文档平台和同步策略

### 3. 技术架构优势
- **统一接口**：所有数据源使用相同的接口规范
- **解耦设计**：核心框架与具体实现完全解耦
- **高可维护性**：新增数据源无需修改核心代码
- **配置灵活**：支持多种同步策略和参数配置
- **监控统一**：统一的监控和运维管理

## 整体架构图

```mermaid
graph TD
    A[文档同步协调器] --> B[通用同步协议层]
    
    B --> C[数据源适配器管理]
    C --> D[飞书适配器]
    C --> E[语雀适配器]
    C --> F[Notion适配器]
    C --> G[钉钉适配器]
    C --> H[其他适配器...]
    
    B --> I[统一事件总线]
    I --> J[事件监听器]
    I --> K[事件处理器]
    I --> L[事件分发器]
    
    B --> M[通用数据模型]
    M --> N[文档实体模型]
    M --> O[协作信息模型]
    M --> P[同步状态模型]
    
    B --> Q[同步策略引擎]
    Q --> R[增量同步策略]
    Q --> S[全量同步策略]
    Q --> T[实时同步策略]
    Q --> U[自定义策略]
    
    D --> V[飞书API客户端]
    E --> W[语雀API客户端]
    F --> X[Notion API客户端]
    G --> Y[钉钉API客户端]
    
    V --> Z[飞书Webhook]
    W --> AA[语雀Webhook]
    X --> BB[Notion Webhook]
    Y --> CC[钉钉Webhook]
    
    B --> DD[配置管理中心]
    DD --> EE[数据源配置]
    DD --> FF[同步策略配置]
    DD --> GG[认证配置]
    
    B --> HH[监控运维中心]
    HH --> II[同步状态监控]
    HH --> JJ[性能指标监控]
    HH --> KK[错误日志管理]
    
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style M fill:#e8f5e8
    style Q fill:#fff3e0
```

## 核心协议定义

### 1. 通用文档同步协议接口

#### 1.1 数据源适配器接口
```java
/**
 * 通用文档数据源适配器接口
 * 所有文档平台适配器都必须实现此接口
 */
public interface DocumentSourceAdapter {
    
    /**
     * 获取适配器名称
     */
    String getAdapterName();
    
    /**
     * 获取支持的文档类型
     */
    Set<DocumentType> getSupportedDocumentTypes();
    
    /**
     * 初始化适配器
     */
    void initialize(AdapterConfig config);
    
    /**
     * 获取认证信息
     */
    AuthenticationInfo authenticate();
    
    /**
     * 获取文档列表
     */
    List<UniversalDocument> getDocumentList(DocumentQuery query);
    
    /**
     * 获取单个文档内容
     */
    UniversalDocument getDocument(String documentId);
    
    /**
     * 获取文档协作信息
     */
    CollaborationInfo getCollaborationInfo(String documentId);
    
    /**
     * 检查文档是否有更新
     */
    boolean hasDocumentUpdated(String documentId, Instant lastSyncTime);
    
    /**
     * 注册Webhook监听
     */
    void registerWebhookListener(WebhookConfig config);
    
    /**
     * 处理Webhook事件
     */
    void handleWebhookEvent(UniversalWebhookEvent event);
    
    /**
     * 健康检查
     */
    HealthStatus getHealthStatus();
}
```

#### 1.2 通用文档数据模型
```java
/**
 * 通用文档实体模型
 * 标准化不同平台的文档数据格式
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UniversalDocument {
    
    // 基本信息
    private String documentId;              // 文档唯一标识
    private String title;                   // 文档标题
    private DocumentType documentType;      // 文档类型
    private String sourceType;              // 数据源类型（feishu/yuque/notion等）
    private String sourceUrl;               // 原始文档URL
    
    // 内容信息
    private List<UniversalContentBlock> contentBlocks;  // 内容块列表
    private Map<String, Object> metadata;               // 元数据
    private String plainTextContent;                    // 纯文本内容
    private String richTextContent;                     // 富文本内容
    
    // 时间信息
    private Instant createdTime;            // 创建时间
    private Instant lastModifiedTime;       // 最后修改时间
    private Instant lastSyncTime;           // 最后同步时间
    
    // 协作信息
    private CollaborationInfo collaborationInfo;  // 协作信息
    private List<DocumentPermission> permissions; // 权限信息
    
    // 同步信息
    private SyncStatus syncStatus;          // 同步状态
    private String syncVersion;             // 同步版本号
    private Map<String, Object> syncMetadata; // 同步元数据
}

/**
 * 通用内容块模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UniversalContentBlock {
    private String blockId;                 // 块唯一标识
    private BlockType blockType;            // 块类型
    private String content;                 // 块内容
    private Map<String, Object> properties; // 块属性
    private List<UniversalContentBlock> children; // 子块
    private StyleInfo styleInfo;            // 样式信息
    private PositionInfo positionInfo;      // 位置信息
}

/**
 * 协作信息模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CollaborationInfo {
    private List<Collaborator> collaborators;  // 协作者列表
    private List<Comment> comments;             // 评论列表
    private List<Revision> revisions;           // 修改历史
    private List<Annotation> annotations;      // 批注列表
    private ShareInfo shareInfo;                // 分享信息
}
```

#### 1.3 通用事件模型
```java
/**
 * 通用Webhook事件模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UniversalWebhookEvent {
    private String eventId;                 // 事件唯一标识
    private String eventType;               // 事件类型
    private String sourceType;              // 数据源类型
    private String documentId;              // 文档ID
    private Instant timestamp;              // 事件时间戳
    private Map<String, Object> eventData;  // 事件数据
    private String signature;               // 事件签名
    private EventMetadata metadata;         // 事件元数据
}

/**
 * 标准化事件类型枚举
 */
public enum UniversalEventType {
    DOCUMENT_CREATED("document.created", "文档创建"),
    DOCUMENT_UPDATED("document.updated", "文档更新"),
    DOCUMENT_DELETED("document.deleted", "文档删除"),
    DOCUMENT_SHARED("document.shared", "文档分享"),
    DOCUMENT_UNSHARED("document.unshared", "取消分享"),
    COLLABORATOR_ADDED("collaborator.added", "协作者添加"),
    COLLABORATOR_REMOVED("collaborator.removed", "协作者移除"),
    COMMENT_ADDED("comment.added", "评论添加"),
    COMMENT_UPDATED("comment.updated", "评论更新"),
    COMMENT_DELETED("comment.deleted", "评论删除");
    
    private final String code;
    private final String description;
    
    UniversalEventType(String code, String description) {
        this.code = code;
        this.description = description;
    }
}
```

### 2. 同步策略引擎

#### 2.1 同步策略接口
```java
/**
 * 通用同步策略接口
 */
public interface SyncStrategy {
    
    /**
     * 获取策略名称
     */
    String getStrategyName();
    
    /**
     * 执行同步
     */
    SyncResult executeSync(SyncContext context);
    
    /**
     * 是否支持该数据源
     */
    boolean supports(String sourceType);
    
    /**
     * 获取同步配置
     */
    SyncConfig getDefaultConfig();
}

/**
 * 同步上下文
 */
@Data
@Builder
public class SyncContext {
    private String sourceType;              // 数据源类型
    private DocumentSourceAdapter adapter;  // 数据源适配器
    private SyncConfig config;              // 同步配置
    private List<String> documentIds;       // 待同步文档ID列表
    private Instant lastSyncTime;           // 上次同步时间
    private Map<String, Object> parameters; // 同步参数
}

/**
 * 同步结果
 */
@Data
@Builder
public class SyncResult {
    private boolean success;                // 同步是否成功
    private int totalCount;                 // 总文档数
    private int successCount;               // 成功同步数
    private int failureCount;               // 失败同步数
    private List<String> failedDocuments;  // 失败文档列表
    private Duration syncDuration;          // 同步耗时
    private String errorMessage;            // 错误信息
    private Map<String, Object> metadata;   // 同步元数据
}
```

#### 2.2 增量同步策略实现
```java
@Component
public class IncrementalSyncStrategy implements SyncStrategy {
    
    @Override
    public String getStrategyName() {
        return "incremental";
    }
    
    @Override
    public SyncResult executeSync(SyncContext context) {
        DocumentSourceAdapter adapter = context.getAdapter();
        Instant lastSyncTime = context.getLastSyncTime();
        
        SyncResult.SyncResultBuilder resultBuilder = SyncResult.builder();
        List<String> failedDocuments = new ArrayList<>();
        
        try {
            // 1. 获取需要同步的文档列表
            DocumentQuery query = DocumentQuery.builder()
                .modifiedAfter(lastSyncTime)
                .build();
            
            List<UniversalDocument> documents = adapter.getDocumentList(query);
            resultBuilder.totalCount(documents.size());
            
            int successCount = 0;
            
            // 2. 逐个同步文档
            for (UniversalDocument doc : documents) {
                try {
                    // 检查文档是否真的有更新
                    if (adapter.hasDocumentUpdated(doc.getDocumentId(), lastSyncTime)) {
                        // 获取完整文档内容
                        UniversalDocument fullDocument = adapter.getDocument(doc.getDocumentId());
                        
                        // 获取协作信息
                        CollaborationInfo collaborationInfo = adapter.getCollaborationInfo(doc.getDocumentId());
                        fullDocument.setCollaborationInfo(collaborationInfo);
                        
                        // 处理文档
                        processDocument(fullDocument);
                        
                        successCount++;
                    }
                } catch (Exception e) {
                    log.error("同步文档失败: documentId={}", doc.getDocumentId(), e);
                    failedDocuments.add(doc.getDocumentId());
                }
            }
            
            resultBuilder
                .success(failedDocuments.isEmpty())
                .successCount(successCount)
                .failureCount(failedDocuments.size())
                .failedDocuments(failedDocuments);
                
        } catch (Exception e) {
            log.error("增量同步执行失败", e);
            resultBuilder
                .success(false)
                .errorMessage(e.getMessage());
        }
        
        return resultBuilder.build();
    }
    
    private void processDocument(UniversalDocument document) {
        // 发送到文档处理管道
        documentProcessingService.processUniversalDocument(document);
    }
}
```

### 3. 配置管理中心

#### 3.1 数据源配置模型
```java
/**
 * 数据源配置
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DataSourceConfig {
    private String sourceType;              // 数据源类型
    private String sourceName;              // 数据源名称
    private boolean enabled;                // 是否启用
    private AuthConfig authConfig;          // 认证配置
    private SyncConfig syncConfig;          // 同步配置
    private WebhookConfig webhookConfig;    // Webhook配置
    private Map<String, Object> customProperties; // 自定义属性
}

/**
 * 认证配置
 */
@Data
@Builder
public class AuthConfig {
    private AuthType authType;              // 认证类型
    private String appId;                   // 应用ID
    private String appSecret;               // 应用密钥
    private String accessToken;             // 访问令牌
    private String refreshToken;            // 刷新令牌
    private Instant tokenExpireTime;        // 令牌过期时间
    private Map<String, String> headers;    // 自定义请求头
}

/**
 * 同步配置
 */
@Data
@Builder
public class SyncConfig {
    private String syncStrategy;            // 同步策略
    private String incrementalCron;         // 增量同步Cron表达式
    private String fullSyncCron;            // 全量同步Cron表达式
    private boolean realtimeSync;           // 是否启用实时同步
    private int batchSize;                  // 批处理大小
    private int maxRetryCount;              // 最大重试次数
    private Duration timeout;               // 超时时间
    private List<String> includeDocumentTypes; // 包含的文档类型
    private List<String> excludeDocumentTypes; // 排除的文档类型
}
```

#### 3.2 配置管理服务
```java
@Service
public class DataSourceConfigService {
    
    @Autowired
    private DataSourceConfigRepository configRepository;
    
    @Autowired
    private ApplicationEventPublisher eventPublisher;
    
    /**
     * 获取所有启用的数据源配置
     */
    public List<DataSourceConfig> getEnabledDataSources() {
        return configRepository.findByEnabledTrue();
    }
    
    /**
     * 获取指定类型的数据源配置
     */
    public Optional<DataSourceConfig> getDataSourceConfig(String sourceType) {
        return configRepository.findBySourceType(sourceType);
    }
    
    /**
     * 更新数据源配置
     */
    public void updateDataSourceConfig(DataSourceConfig config) {
        configRepository.save(config);
        
        // 发布配置更新事件
        eventPublisher.publishEvent(new DataSourceConfigUpdatedEvent(config));
    }
    
    /**
     * 验证数据源配置
     */
    public ConfigValidationResult validateConfig(DataSourceConfig config) {
        try {
            // 创建适配器实例
            DocumentSourceAdapter adapter = createAdapter(config.getSourceType());
            adapter.initialize(AdapterConfig.fromDataSourceConfig(config));
            
            // 测试认证
            AuthenticationInfo authInfo = adapter.authenticate();
            if (!authInfo.isValid()) {
                return ConfigValidationResult.failure("认证失败: " + authInfo.getErrorMessage());
            }
            
            // 测试API连接
            HealthStatus healthStatus = adapter.getHealthStatus();
            if (!healthStatus.isHealthy()) {
                return ConfigValidationResult.failure("API连接失败: " + healthStatus.getErrorMessage());
            }
            
            return ConfigValidationResult.success();
            
        } catch (Exception e) {
            return ConfigValidationResult.failure("配置验证失败: " + e.getMessage());
        }
    }
}
```

### 4. 事件总线系统

#### 4.1 事件总线接口
```java
/**
 * 通用事件总线
 */
public interface UniversalEventBus {
    
    /**
     * 发布事件
     */
    void publishEvent(UniversalWebhookEvent event);
    
    /**
     * 订阅事件
     */
    void subscribe(String eventType, EventHandler handler);
    
    /**
     * 取消订阅
     */
    void unsubscribe(String eventType, EventHandler handler);
    
    /**
     * 获取事件处理器
     */
    List<EventHandler> getHandlers(String eventType);
}

/**
 * 事件处理器接口
 */
public interface EventHandler {
    
    /**
     * 处理事件
     */
    void handleEvent(UniversalWebhookEvent event);
    
    /**
     * 获取支持的事件类型
     */
    Set<String> getSupportedEventTypes();
    
    /**
     * 获取处理器优先级
     */
    int getPriority();
}
```

#### 4.2 事件总线实现
```java
@Component
public class DefaultUniversalEventBus implements UniversalEventBus {
    
    private final Map<String, List<EventHandler>> eventHandlers = new ConcurrentHashMap<>();
    private final ExecutorService eventExecutor;
    
    public DefaultUniversalEventBus() {
        this.eventExecutor = Executors.newFixedThreadPool(10, 
            new ThreadFactoryBuilder().setNameFormat("event-handler-%d").build());
    }
    
    @Override
    public void publishEvent(UniversalWebhookEvent event) {
        String eventType = event.getEventType();
        List<EventHandler> handlers = eventHandlers.get(eventType);
        
        if (handlers != null && !handlers.isEmpty()) {
            // 异步处理事件
            eventExecutor.submit(() -> {
                for (EventHandler handler : handlers) {
                    try {
                        handler.handleEvent(event);
                    } catch (Exception e) {
                        log.error("事件处理失败: eventType={}, handler={}", 
                            eventType, handler.getClass().getSimpleName(), e);
                    }
                }
            });
        }
    }
    
    @Override
    public void subscribe(String eventType, EventHandler handler) {
        eventHandlers.computeIfAbsent(eventType, k -> new ArrayList<>()).add(handler);
        
        // 按优先级排序
        eventHandlers.get(eventType).sort(Comparator.comparingInt(EventHandler::getPriority));
    }
}
```

## 具体数据源适配器实现

### 1. 飞书适配器实现

#### 1.1 飞书适配器
```java
@Component
public class FeishuDocumentAdapter implements DocumentSourceAdapter {

    @Autowired
    private FeishuApiClient feishuApiClient;

    private AdapterConfig config;

    @Override
    public String getAdapterName() {
        return "feishu";
    }

    @Override
    public Set<DocumentType> getSupportedDocumentTypes() {
        return Set.of(
            DocumentType.DOCUMENT,
            DocumentType.SPREADSHEET,
            DocumentType.BITABLE,
            DocumentType.MINDNOTE,
            DocumentType.WHITEBOARD
        );
    }

    @Override
    public void initialize(AdapterConfig config) {
        this.config = config;
        feishuApiClient.initialize(config.getAuthConfig());
    }

    @Override
    public AuthenticationInfo authenticate() {
        try {
            String accessToken = feishuApiClient.getAccessToken();
            return AuthenticationInfo.builder()
                .valid(true)
                .accessToken(accessToken)
                .build();
        } catch (Exception e) {
            return AuthenticationInfo.builder()
                .valid(false)
                .errorMessage(e.getMessage())
                .build();
        }
    }

    @Override
    public List<UniversalDocument> getDocumentList(DocumentQuery query) {
        String accessToken = feishuApiClient.getAccessToken();
        List<FeishuDocumentInfo> feishuDocs = feishuApiClient.getDocumentList(accessToken, query);

        return feishuDocs.stream()
            .map(this::convertToUniversalDocument)
            .collect(Collectors.toList());
    }

    @Override
    public UniversalDocument getDocument(String documentId) {
        String accessToken = feishuApiClient.getAccessToken();
        FeishuDocumentInfo docInfo = feishuApiClient.getDocumentInfo(documentId, accessToken);
        FeishuDocContent docContent = feishuApiClient.getDocumentContent(documentId, accessToken);

        return convertToUniversalDocument(docInfo, docContent);
    }

    @Override
    public CollaborationInfo getCollaborationInfo(String documentId) {
        String accessToken = feishuApiClient.getAccessToken();

        List<FeishuCollaborator> collaborators = feishuApiClient.getDocumentCollaborators(documentId, accessToken);
        List<FeishuComment> comments = feishuApiClient.getDocumentComments(documentId, accessToken);
        List<FeishuRevision> revisions = feishuApiClient.getDocumentRevisions(documentId, accessToken);

        return convertToCollaborationInfo(collaborators, comments, revisions);
    }

    @Override
    public void handleWebhookEvent(UniversalWebhookEvent event) {
        // 将通用事件转换为飞书特定处理
        FeishuDocumentEvent feishuEvent = convertToFeishuEvent(event);
        feishuWebhookService.handleFeishuDocumentEvent(feishuEvent);
    }

    private UniversalDocument convertToUniversalDocument(FeishuDocumentInfo docInfo) {
        return UniversalDocument.builder()
            .documentId(docInfo.getDocumentId())
            .title(docInfo.getTitle())
            .documentType(mapFeishuDocumentType(docInfo.getType()))
            .sourceType("feishu")
            .sourceUrl(docInfo.getUrl())
            .createdTime(docInfo.getCreatedTime())
            .lastModifiedTime(docInfo.getLastModifiedTime())
            .build();
    }
}
```

### 2. 语雀适配器实现

#### 2.1 语雀适配器
```java
@Component
public class YuqueDocumentAdapter implements DocumentSourceAdapter {

    @Autowired
    private YuqueApiClient yuqueApiClient;

    @Override
    public String getAdapterName() {
        return "yuque";
    }

    @Override
    public Set<DocumentType> getSupportedDocumentTypes() {
        return Set.of(
            DocumentType.DOCUMENT,
            DocumentType.BOARD,
            DocumentType.TABLE
        );
    }

    @Override
    public List<UniversalDocument> getDocumentList(DocumentQuery query) {
        String accessToken = yuqueApiClient.getAccessToken();
        List<YuqueDocument> yuqueDocs = yuqueApiClient.getDocumentList(accessToken, query);

        return yuqueDocs.stream()
            .map(this::convertToUniversalDocument)
            .collect(Collectors.toList());
    }

    @Override
    public UniversalDocument getDocument(String documentId) {
        String accessToken = yuqueApiClient.getAccessToken();
        YuqueDocument yuqueDoc = yuqueApiClient.getDocument(documentId, accessToken);
        YuqueDocumentContent content = yuqueApiClient.getDocumentContent(documentId, accessToken);

        return convertToUniversalDocument(yuqueDoc, content);
    }

    @Override
    public CollaborationInfo getCollaborationInfo(String documentId) {
        String accessToken = yuqueApiClient.getAccessToken();

        List<YuqueCollaborator> collaborators = yuqueApiClient.getCollaborators(documentId, accessToken);
        List<YuqueComment> comments = yuqueApiClient.getComments(documentId, accessToken);

        return convertToCollaborationInfo(collaborators, comments);
    }

    private UniversalDocument convertToUniversalDocument(YuqueDocument yuqueDoc) {
        return UniversalDocument.builder()
            .documentId(yuqueDoc.getId())
            .title(yuqueDoc.getTitle())
            .documentType(mapYuqueDocumentType(yuqueDoc.getFormat()))
            .sourceType("yuque")
            .sourceUrl(yuqueDoc.getUrl())
            .plainTextContent(yuqueDoc.getBodyPlain())
            .richTextContent(yuqueDoc.getBodyHtml())
            .createdTime(yuqueDoc.getCreatedAt())
            .lastModifiedTime(yuqueDoc.getUpdatedAt())
            .build();
    }
}
```

### 3. Notion适配器实现

#### 3.1 Notion适配器
```java
@Component
public class NotionDocumentAdapter implements DocumentSourceAdapter {

    @Autowired
    private NotionApiClient notionApiClient;

    @Override
    public String getAdapterName() {
        return "notion";
    }

    @Override
    public Set<DocumentType> getSupportedDocumentTypes() {
        return Set.of(
            DocumentType.PAGE,
            DocumentType.DATABASE,
            DocumentType.BLOCK
        );
    }

    @Override
    public List<UniversalDocument> getDocumentList(DocumentQuery query) {
        String accessToken = notionApiClient.getAccessToken();
        List<NotionPage> notionPages = notionApiClient.searchPages(accessToken, query);

        return notionPages.stream()
            .map(this::convertToUniversalDocument)
            .collect(Collectors.toList());
    }

    @Override
    public UniversalDocument getDocument(String documentId) {
        String accessToken = notionApiClient.getAccessToken();
        NotionPage page = notionApiClient.getPage(documentId, accessToken);
        List<NotionBlock> blocks = notionApiClient.getPageBlocks(documentId, accessToken);

        return convertToUniversalDocument(page, blocks);
    }

    @Override
    public CollaborationInfo getCollaborationInfo(String documentId) {
        String accessToken = notionApiClient.getAccessToken();

        // Notion的协作信息相对简单
        NotionPage page = notionApiClient.getPage(documentId, accessToken);
        List<NotionUser> users = notionApiClient.getPageUsers(documentId, accessToken);

        return convertToCollaborationInfo(page, users);
    }

    private UniversalDocument convertToUniversalDocument(NotionPage page, List<NotionBlock> blocks) {
        List<UniversalContentBlock> contentBlocks = blocks.stream()
            .map(this::convertNotionBlock)
            .collect(Collectors.toList());

        return UniversalDocument.builder()
            .documentId(page.getId())
            .title(extractPageTitle(page))
            .documentType(DocumentType.PAGE)
            .sourceType("notion")
            .sourceUrl(page.getUrl())
            .contentBlocks(contentBlocks)
            .createdTime(page.getCreatedTime())
            .lastModifiedTime(page.getLastEditedTime())
            .build();
    }

    private UniversalContentBlock convertNotionBlock(NotionBlock block) {
        return UniversalContentBlock.builder()
            .blockId(block.getId())
            .blockType(mapNotionBlockType(block.getType()))
            .content(extractBlockContent(block))
            .properties(block.getProperties())
            .children(block.getChildren().stream()
                .map(this::convertNotionBlock)
                .collect(Collectors.toList()))
            .build();
    }
}
```

## 统一同步协调器

### 1. 文档同步协调器
```java
@Service
public class UniversalDocumentSyncCoordinator {

    @Autowired
    private DataSourceConfigService configService;

    @Autowired
    private UniversalEventBus eventBus;

    @Autowired
    private SyncStrategyFactory strategyFactory;

    private final Map<String, DocumentSourceAdapter> adapters = new ConcurrentHashMap<>();

    @PostConstruct
    public void initialize() {
        // 初始化所有启用的数据源适配器
        List<DataSourceConfig> configs = configService.getEnabledDataSources();

        for (DataSourceConfig config : configs) {
            try {
                DocumentSourceAdapter adapter = createAdapter(config.getSourceType());
                adapter.initialize(AdapterConfig.fromDataSourceConfig(config));
                adapters.put(config.getSourceType(), adapter);

                // 注册Webhook监听
                if (config.getWebhookConfig() != null && config.getWebhookConfig().isEnabled()) {
                    adapter.registerWebhookListener(config.getWebhookConfig());
                }

                log.info("数据源适配器初始化成功: {}", config.getSourceType());
            } catch (Exception e) {
                log.error("数据源适配器初始化失败: {}", config.getSourceType(), e);
            }
        }

        // 注册事件处理器
        registerEventHandlers();
    }

    /**
     * 执行全量同步
     */
    public Map<String, SyncResult> performFullSync() {
        Map<String, SyncResult> results = new HashMap<>();

        for (Map.Entry<String, DocumentSourceAdapter> entry : adapters.entrySet()) {
            String sourceType = entry.getKey();
            DocumentSourceAdapter adapter = entry.getValue();

            try {
                SyncStrategy strategy = strategyFactory.getStrategy("full");
                SyncContext context = SyncContext.builder()
                    .sourceType(sourceType)
                    .adapter(adapter)
                    .config(getSourceSyncConfig(sourceType))
                    .build();

                SyncResult result = strategy.executeSync(context);
                results.put(sourceType, result);

                log.info("全量同步完成: sourceType={}, result={}", sourceType, result);
            } catch (Exception e) {
                log.error("全量同步失败: sourceType={}", sourceType, e);
                results.put(sourceType, SyncResult.builder()
                    .success(false)
                    .errorMessage(e.getMessage())
                    .build());
            }
        }

        return results;
    }

    /**
     * 执行增量同步
     */
    public Map<String, SyncResult> performIncrementalSync() {
        Map<String, SyncResult> results = new HashMap<>();

        for (Map.Entry<String, DocumentSourceAdapter> entry : adapters.entrySet()) {
            String sourceType = entry.getKey();
            DocumentSourceAdapter adapter = entry.getValue();

            try {
                SyncStrategy strategy = strategyFactory.getStrategy("incremental");
                SyncContext context = SyncContext.builder()
                    .sourceType(sourceType)
                    .adapter(adapter)
                    .config(getSourceSyncConfig(sourceType))
                    .lastSyncTime(getLastSyncTime(sourceType))
                    .build();

                SyncResult result = strategy.executeSync(context);
                results.put(sourceType, result);

                // 更新最后同步时间
                updateLastSyncTime(sourceType, Instant.now());

                log.info("增量同步完成: sourceType={}, result={}", sourceType, result);
            } catch (Exception e) {
                log.error("增量同步失败: sourceType={}", sourceType, e);
                results.put(sourceType, SyncResult.builder()
                    .success(false)
                    .errorMessage(e.getMessage())
                    .build());
            }
        }

        return results;
    }

    /**
     * 处理Webhook事件
     */
    public void handleWebhookEvent(String sourceType, UniversalWebhookEvent event) {
        DocumentSourceAdapter adapter = adapters.get(sourceType);
        if (adapter != null) {
            try {
                adapter.handleWebhookEvent(event);

                // 发布到事件总线
                eventBus.publishEvent(event);

                log.info("Webhook事件处理完成: sourceType={}, eventType={}",
                    sourceType, event.getEventType());
            } catch (Exception e) {
                log.error("Webhook事件处理失败: sourceType={}, eventType={}",
                    sourceType, event.getEventType(), e);
            }
        }
    }

    private void registerEventHandlers() {
        // 注册文档更新事件处理器
        eventBus.subscribe("document.updated", new DocumentUpdatedEventHandler());
        eventBus.subscribe("document.created", new DocumentCreatedEventHandler());
        eventBus.subscribe("document.deleted", new DocumentDeletedEventHandler());
        eventBus.subscribe("collaborator.added", new CollaboratorAddedEventHandler());
        eventBus.subscribe("comment.added", new CommentAddedEventHandler());
    }
}
```

### 2. 定时同步调度器
```java
@Component
public class UniversalSyncScheduler {

    @Autowired
    private UniversalDocumentSyncCoordinator syncCoordinator;

    @Autowired
    private DataSourceConfigService configService;

    /**
     * 增量同步任务
     * 根据各数据源配置的Cron表达式执行
     */
    @Scheduled(fixedRate = 60000) // 每分钟检查一次
    public void checkIncrementalSync() {
        List<DataSourceConfig> configs = configService.getEnabledDataSources();

        for (DataSourceConfig config : configs) {
            if (shouldExecuteIncrementalSync(config)) {
                executeIncrementalSyncForSource(config.getSourceType());
            }
        }
    }

    /**
     * 全量同步任务
     * 每天凌晨2点执行
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void performFullSync() {
        log.info("开始执行全量同步任务");

        Map<String, SyncResult> results = syncCoordinator.performFullSync();

        // 记录同步结果
        for (Map.Entry<String, SyncResult> entry : results.entrySet()) {
            String sourceType = entry.getKey();
            SyncResult result = entry.getValue();

            if (result.isSuccess()) {
                log.info("全量同步成功: sourceType={}, successCount={}, duration={}",
                    sourceType, result.getSuccessCount(), result.getSyncDuration());
            } else {
                log.error("全量同步失败: sourceType={}, error={}",
                    sourceType, result.getErrorMessage());
            }
        }
    }

    private boolean shouldExecuteIncrementalSync(DataSourceConfig config) {
        // 检查是否到了执行增量同步的时间
        String cronExpression = config.getSyncConfig().getIncrementalCron();
        return CronUtils.shouldExecute(cronExpression);
    }

    private void executeIncrementalSyncForSource(String sourceType) {
        try {
            Map<String, SyncResult> results = syncCoordinator.performIncrementalSync();
            SyncResult result = results.get(sourceType);

            if (result != null && result.isSuccess()) {
                log.info("增量同步成功: sourceType={}, successCount={}",
                    sourceType, result.getSuccessCount());
            }
        } catch (Exception e) {
            log.error("增量同步执行失败: sourceType={}", sourceType, e);
        }
    }
}
```

## 配置示例

### 1. 应用配置文件
```yaml
# application.yml
universal-document-sync:
  enabled: true
  event-bus:
    thread-pool-size: 10
    queue-capacity: 1000

  data-sources:
    feishu:
      enabled: true
      auth:
        app-id: ${FEISHU_APP_ID}
        app-secret: ${FEISHU_APP_SECRET}
      sync:
        strategy: incremental
        incremental-cron: "0 0 * * * ?"
        full-sync-cron: "0 0 2 * * ?"
        batch-size: 100
        timeout: 30s
      webhook:
        enabled: true
        verification-token: ${FEISHU_WEBHOOK_TOKEN}
        encrypt-key: ${FEISHU_WEBHOOK_ENCRYPT_KEY}

    yuque:
      enabled: true
      auth:
        access-token: ${YUQUE_ACCESS_TOKEN}
      sync:
        strategy: incremental
        incremental-cron: "0 30 * * * ?"
        batch-size: 50
        timeout: 30s
      webhook:
        enabled: false

    notion:
      enabled: true
      auth:
        access-token: ${NOTION_ACCESS_TOKEN}
      sync:
        strategy: incremental
        incremental-cron: "0 15 * * * ?"
        batch-size: 50
        timeout: 30s
      webhook:
        enabled: true
        verification-token: ${NOTION_WEBHOOK_TOKEN}
```

### 2. 数据库配置
```sql
-- 通用文档同步记录表
CREATE TABLE universal_document_sync_record (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    source_type VARCHAR(50) NOT NULL COMMENT '数据源类型',
    document_id VARCHAR(255) NOT NULL COMMENT '文档ID',
    document_title VARCHAR(500) COMMENT '文档标题',
    document_type VARCHAR(50) COMMENT '文档类型',
    last_sync_time TIMESTAMP COMMENT '最后同步时间',
    sync_status VARCHAR(20) COMMENT '同步状态',
    sync_version VARCHAR(100) COMMENT '同步版本',
    error_message TEXT COMMENT '错误信息',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_source_document (source_type, document_id),
    INDEX idx_source_type (source_type),
    INDEX idx_sync_status (sync_status),
    INDEX idx_last_sync_time (last_sync_time)
);

-- 数据源配置表
CREATE TABLE data_source_config (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    source_type VARCHAR(50) UNIQUE NOT NULL COMMENT '数据源类型',
    source_name VARCHAR(100) COMMENT '数据源名称',
    enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    auth_config JSON COMMENT '认证配置',
    sync_config JSON COMMENT '同步配置',
    webhook_config JSON COMMENT 'Webhook配置',
    custom_properties JSON COMMENT '自定义属性',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## 监控与运维

### 1. 同步状态监控API
```java
@RestController
@RequestMapping("/api/universal-sync")
public class UniversalSyncMonitorController {

    @Autowired
    private UniversalDocumentSyncCoordinator syncCoordinator;

    @Autowired
    private UniversalDocumentSyncRepository syncRepository;

    /**
     * 获取所有数据源的同步状态
     */
    @GetMapping("/status")
    public Map<String, SyncStatusSummary> getAllSyncStatus() {
        Map<String, SyncStatusSummary> statusMap = new HashMap<>();

        List<String> sourceTypes = syncRepository.findDistinctSourceTypes();

        for (String sourceType : sourceTypes) {
            SyncStatusSummary summary = syncRepository.getSyncStatusSummary(sourceType);
            statusMap.put(sourceType, summary);
        }

        return statusMap;
    }

    /**
     * 获取指定数据源的同步状态
     */
    @GetMapping("/status/{sourceType}")
    public SyncStatusSummary getSyncStatus(@PathVariable String sourceType) {
        return syncRepository.getSyncStatusSummary(sourceType);
    }

    /**
     * 手动触发同步
     */
    @PostMapping("/trigger/{sourceType}")
    public ResponseEntity<String> triggerSync(
            @PathVariable String sourceType,
            @RequestParam(defaultValue = "incremental") String syncType) {

        try {
            if ("full".equals(syncType)) {
                syncCoordinator.performFullSync();
            } else {
                syncCoordinator.performIncrementalSync();
            }

            return ResponseEntity.ok("同步任务已触发");
        } catch (Exception e) {
            return ResponseEntity.status(500).body("同步任务触发失败: " + e.getMessage());
        }
    }

    /**
     * 获取同步失败的文档列表
     */
    @GetMapping("/failed/{sourceType}")
    public List<UniversalDocumentSyncRecord> getFailedDocuments(@PathVariable String sourceType) {
        return syncRepository.findBySourceTypeAndSyncStatus(sourceType, SyncStatus.FAILED);
    }
}
```

### 2. 性能指标监控
```java
@Component
public class UniversalSyncMetrics {

    private final MeterRegistry meterRegistry;
    private final Map<String, Counter> syncSuccessCounters = new ConcurrentHashMap<>();
    private final Map<String, Counter> syncFailureCounters = new ConcurrentHashMap<>();
    private final Map<String, Timer> syncDurationTimers = new ConcurrentHashMap<>();

    public UniversalSyncMetrics(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
    }

    public void recordSyncSuccess(String sourceType, int documentCount) {
        getSuccessCounter(sourceType).increment(documentCount);
    }

    public void recordSyncFailure(String sourceType, int documentCount) {
        getFailureCounter(sourceType).increment(documentCount);
    }

    public Timer.Sample startSyncTimer(String sourceType) {
        return Timer.start(meterRegistry);
    }

    public void recordSyncDuration(String sourceType, Timer.Sample sample) {
        sample.stop(getDurationTimer(sourceType));
    }

    private Counter getSuccessCounter(String sourceType) {
        return syncSuccessCounters.computeIfAbsent(sourceType, type ->
            Counter.builder("universal.sync.success")
                .description("通用文档同步成功次数")
                .tag("source_type", type)
                .register(meterRegistry));
    }

    private Counter getFailureCounter(String sourceType) {
        return syncFailureCounters.computeIfAbsent(sourceType, type ->
            Counter.builder("universal.sync.failure")
                .description("通用文档同步失败次数")
                .tag("source_type", type)
                .register(meterRegistry));
    }

    private Timer getDurationTimer(String sourceType) {
        return syncDurationTimers.computeIfAbsent(sourceType, type ->
            Timer.builder("universal.sync.duration")
                .description("通用文档同步耗时")
                .tag("source_type", type)
                .register(meterRegistry));
    }
}
```

## 扩展新数据源指南

### 1. 实现适配器接口
```java
@Component
public class NewPlatformAdapter implements DocumentSourceAdapter {

    @Override
    public String getAdapterName() {
        return "new-platform";
    }

    @Override
    public Set<DocumentType> getSupportedDocumentTypes() {
        // 返回支持的文档类型
        return Set.of(DocumentType.DOCUMENT, DocumentType.SPREADSHEET);
    }

    @Override
    public void initialize(AdapterConfig config) {
        // 初始化API客户端和配置
    }

    @Override
    public AuthenticationInfo authenticate() {
        // 实现认证逻辑
        return AuthenticationInfo.builder().valid(true).build();
    }

    @Override
    public List<UniversalDocument> getDocumentList(DocumentQuery query) {
        // 实现文档列表获取逻辑
        return Collections.emptyList();
    }

    @Override
    public UniversalDocument getDocument(String documentId) {
        // 实现单个文档获取逻辑
        return UniversalDocument.builder().build();
    }

    @Override
    public CollaborationInfo getCollaborationInfo(String documentId) {
        // 实现协作信息获取逻辑
        return CollaborationInfo.builder().build();
    }

    @Override
    public boolean hasDocumentUpdated(String documentId, Instant lastSyncTime) {
        // 实现文档更新检查逻辑
        return false;
    }

    @Override
    public void registerWebhookListener(WebhookConfig config) {
        // 实现Webhook注册逻辑
    }

    @Override
    public void handleWebhookEvent(UniversalWebhookEvent event) {
        // 实现Webhook事件处理逻辑
    }

    @Override
    public HealthStatus getHealthStatus() {
        // 实现健康检查逻辑
        return HealthStatus.healthy();
    }
}
```

### 2. 添加配置
```yaml
# 在application.yml中添加新数据源配置
universal-document-sync:
  data-sources:
    new-platform:
      enabled: true
      auth:
        api-key: ${NEW_PLATFORM_API_KEY}
      sync:
        strategy: incremental
        incremental-cron: "0 45 * * * ?"
        batch-size: 50
        timeout: 30s
      webhook:
        enabled: true
        secret: ${NEW_PLATFORM_WEBHOOK_SECRET}
```

### 3. 注册适配器
```java
@Configuration
public class DataSourceAdapterConfig {

    @Bean
    public DocumentSourceAdapterRegistry adapterRegistry(
            List<DocumentSourceAdapter> adapters) {

        DocumentSourceAdapterRegistry registry = new DocumentSourceAdapterRegistry();

        for (DocumentSourceAdapter adapter : adapters) {
            registry.registerAdapter(adapter.getAdapterName(), adapter);
        }

        return registry;
    }
}
```

## 总结

通用文档同步协议为RAG系统提供了一个标准化、可扩展的多数据源文档接入框架。通过统一的接口定义、事件模型和配置管理，实现了"一次开发，多平台复用"的设计目标。该协议具有以下优势：

1. **标准化**：统一的接口和数据模型，降低开发和维护成本
2. **可扩展**：插件化架构，易于扩展新的文档平台
3. **配置驱动**：灵活的配置管理，支持多种同步策略
4. **事件驱动**：统一的事件总线，支持实时同步和异步处理
5. **监控完善**：全面的监控和运维支持，确保系统稳定运行

通过这个通用协议，您可以轻松接入飞书、语雀、Notion、钉钉等多个文档平台，为RAG系统提供丰富的知识来源。
```
